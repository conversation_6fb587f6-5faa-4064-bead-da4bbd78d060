<script>
	import { Countries } from '$lib/constants/Countries';
	import { createEventDispatcher } from 'svelte';
	import RegistrationRequest from '$lib/RegistrationRequest.svelte';
	import { theme, getThemeClasses } from '$lib/stores/theme';

	const dispatch = createEventDispatcher();

	let email = '';
	let secretKey = '';
	let selectedCountry = '';
	let loading = false;
	let error = '';
	let showRegistrationRequest = false;

	// Reactive theme classes
	$: textPrimary = getThemeClasses('text-gray-900', 'text-slate-200', $theme);
	$: textMuted = getThemeClasses('text-gray-500', 'text-white/70', $theme);

	async function handleLogin() {
		if (!email || !secretKey || !selectedCountry) {
			error = 'Please fill in all fields';
			return;
		}

		loading = true;
		error = '';

		try {
			const response = await fetch('/api/login', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					email: email,
					secretKey: secretKey,
					country_code: selectedCountry
				})
			});

			if (response.ok) {
				const data = await response.json();
				// Store user data in localStorage
				localStorage.setItem('user', JSON.stringify({
					email: email,
					country_code: selectedCountry,
					...data.user  // Use data.user instead of data
				}));
				// Dispatch login success event
				dispatch('login', {
					email: email,
					country_code: selectedCountry,
					...data.user  // Use data.user instead of data
				});
			} else {
				const errorText = await response.text();
				error = errorText || 'Login failed';
			}
		} catch (err) {
			error = 'Network error. Please try again.';
		}

		loading = false;
	}

	function showRegistrationForm() {
		showRegistrationRequest = true;
	}

	function hideRegistrationForm() {
		showRegistrationRequest = false;
	}
</script>

{#if showRegistrationRequest}
	<RegistrationRequest on:back={hideRegistrationForm} />
{:else}
	<div class="w-full">
		<div
			class="text-center md:text-left font-bold {textPrimary} text-4xl md:text-5xl mb-8 md:mb-16 w-full"
		>
			Get curated show <br class="hidden md:block" /> and movie recommendations
			<br class="hidden md:block" /> with Open AI
		</div>

		<div class="max-w-md mx-auto md:mx-0 bg-black/10 dark:bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8">
		<h3 class="{textPrimary} text-xl font-semibold mb-4">Login to Continue</h3>

		{#if error}
			<div class="bg-red-500/20 border border-red-500 {textMuted} px-4 py-2 rounded mb-4">
				{error}
			</div>
		{/if}

		<form on:submit|preventDefault={handleLogin} class="space-y-4">
			<div>
				<label for="email" class="block {textPrimary} text-sm font-medium mb-2">
					Email Address
				</label>
				<input
					id="email"
					type="email"
					bind:value={email}
					required
					class="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-md {textPrimary} placeholder-{textMuted} focus:outline-none focus:ring-2 focus:ring-turquoise-500 focus:border-transparent"
					placeholder="Enter your email"
				/>
			</div>

			<div>
				<label for="secretKey" class="block {textPrimary} text-sm font-medium mb-2">
					Secret Key
				</label>
				<input
					id="secretKey"
					type="password"
					bind:value={secretKey}
					required
					class="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-md {textPrimary} placeholder-{textMuted} focus:outline-none focus:ring-2 focus:ring-turquoise-500 focus:border-transparent"
					placeholder="Enter your secret key"
				/>
			</div>

			<div>
				<label for="country" class="block {textPrimary} text-sm font-medium mb-2">
					Country
				</label>
				<select
					id="country"
					bind:value={selectedCountry}
					required
					class="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-md {textPrimary} focus:outline-none focus:ring-2 focus:ring-turquoise-500 focus:border-transparent"
				>
					<option value="" disabled class="placeholder-{textMuted}">Select your country</option>
					{#each Countries as country}
						<option value={country.country_code} class="bg-slate-800 text-white">
							{country.country_name}
						</option>
					{/each}
				</select>
			</div>

			<button
				type="submit"
				disabled={loading}
				class="w-full bg-turquoise-600 hover:bg-turquoise-700 disabled:bg-turquoise-600/50 disabled:cursor-not-allowed {textPrimary} font-medium py-3 px-6 rounded-md transition-colors"
			>
				{loading ? 'Logging in...' : 'Login'}
			</button>
		</form>

		<div class="text-center mt-4">
			<p class="{textMuted} text-sm mb-2">Don't have access yet?</p>
			<button
				on:click={showRegistrationForm}
				class="text-turquoise-400 hover:text-turquoise-300 text-sm underline"
			>
				Request Access
			</button>
		</div>
	</div>
</div>
{/if}
