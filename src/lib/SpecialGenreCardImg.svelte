<script>
    import { slide } from 'svelte/transition';

    export let specialGenres;
    export let specialGenreSelected = '';

    let showAll = false;
    const itemsPerRow = 3;

    function toggleShowAll() {
        showAll = !showAll;
    }
</script>

<div class="mb-4 font-semibold text-2xl text-white">
    What kind of movie magic are you in the mood for today? Choose your cinematic adventure flavor! Our quirky genre buffet awaits.
</div>

<!-- Always show the first row -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
    {#each specialGenres.slice(0, itemsPerRow) as genre (genre.genre_id)}
        <label
            class={`relative rounded-lg overflow-hidden shadow-lg p-6 text-white bg-cover bg-center cursor-pointer ${
                specialGenreSelected.startsWith(genre.name + ',') ? 'border-4 border-turquoise-800' : 'border-4 border-gray-800'
            }`}
            style="background-image: url('/spgenres_gif/{genre.bg_image}');"
        >
            <input
                class="hidden"
                type="radio"
                bind:group={specialGenreSelected}
                name="specialGenreSelected"
                value={`${genre.name}, a special genre where you ${genre.description}`}
            />
            <div class="absolute inset-0 bg-black/50"></div>
            <div class="relative z-10 always-white-text">
                <h3 class="text-2xl font-bold mb-2">{genre.name}</h3>
                <p class="text-sm">{genre.description}</p>
            </div>
        </label>
    {/each}
</div>

{#if specialGenres.length > itemsPerRow}
    {#if showAll}
        <div
            transition:slide
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-4"
        >
            {#each specialGenres.slice(itemsPerRow) as genre (genre.genre_id)}
                <label
                    class={`relative rounded-lg overflow-hidden shadow-lg p-6 text-white bg-cover bg-center cursor-pointer ${
                        specialGenreSelected.startsWith(genre.name + ',') ? 'border-4 border-turquoise-800' : 'border-4 border-gray-800'
                    }`}
                    style="background-image: url('/spgenres_gif/{genre.bg_image}');"
                >
                    <input
                        class="hidden"
                        type="radio"
                        bind:group={specialGenreSelected}
                        name="specialGenreSelected"
                        value={`${genre.name}, a special genre where you ${genre.description}`}
                    />
                    <div class="absolute inset-0 bg-black/50"></div>
                    <div class="relative z-10 always-white-text">
                        <h3 class="text-2xl font-bold mb-2">{genre.name}</h3>
                        <p class="text-sm">{genre.description}</p>
                    </div>
                </label>
            {/each}
        </div>
        <!-- Show less button at the bottom -->
        <div class="mt-2 flex justify-center">
            <button
                class="text-turquoise-400 hover:text-turquoise-300 hover:underline focus:outline-none transition-all"
                on:click={toggleShowAll}
                aria-expanded={showAll}
            >
                See less
            </button>
        </div>
    {:else}
        <!-- See more button after first row -->
        <div class="mt-2 flex justify-center">
            <button
                class="text-turquoise-400 hover:text-turquoise-300 hover:underline focus:outline-none transition-all"
                on:click={toggleShowAll}
                aria-expanded={showAll}
            >
                See more
            </button>
        </div>
    {/if}
{/if}